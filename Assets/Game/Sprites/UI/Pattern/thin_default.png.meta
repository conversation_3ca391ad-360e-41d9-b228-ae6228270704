fileFormatVersion: 2
guid: 3d187f18a56784af58a7b6eb8604a792
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 256
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: thin_default_0
      rect:
        serializedVersion: 2
        x: 0
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b9ae0f823f79a4c5182957e426bee25e
      internalID: -543733509
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_1
      rect:
        serializedVersion: 2
        x: 256
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1293d7e8d6cac43abb9fa77b34f370ab
      internalID: 1637635196
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_2
      rect:
        serializedVersion: 2
        x: 512
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 53f9371b8c0f74ead9d7758616f4b461
      internalID: 1154087993
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_3
      rect:
        serializedVersion: 2
        x: 768
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c1aedb0b7d9cd432191f1464d05a869d
      internalID: 1043700635
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_4
      rect:
        serializedVersion: 2
        x: 1024
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 72b061abc5ac5480caaf46f850301675
      internalID: 1466820462
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_5
      rect:
        serializedVersion: 2
        x: 0
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7afe1371bf2e4479194142e1ff6c666c
      internalID: 1940972834
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_6
      rect:
        serializedVersion: 2
        x: 256
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b5d22972711f748c888dadb97f8978fc
      internalID: 60339863
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_7
      rect:
        serializedVersion: 2
        x: 512
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2e0fc2625d62d4fb1ad562bafae6252f
      internalID: 437715061
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_8
      rect:
        serializedVersion: 2
        x: 768
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 33061072915504b249ec7efb880ce685
      internalID: -376805070
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_9
      rect:
        serializedVersion: 2
        x: 1024
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5d46d14b930824733a09446fc6ee30fb
      internalID: 1796675843
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_10
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 681e721058a464277849b21671d0895a
      internalID: 1304389951
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_11
      rect:
        serializedVersion: 2
        x: 256
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1183ecc3c65e0441f84d017b88b1678a
      internalID: 7237954
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_12
      rect:
        serializedVersion: 2
        x: 512
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5af5667c79b9345298e488829702466d
      internalID: -515425806
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_13
      rect:
        serializedVersion: 2
        x: 768
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4105c69b1be7a4944a64edd19c17c2e7
      internalID: 284853848
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_14
      rect:
        serializedVersion: 2
        x: 1024
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9051a61250e1f45079b0d3e0bd0c5c04
      internalID: 955299104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_15
      rect:
        serializedVersion: 2
        x: 0
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 184551c8f12794558ac02cbfd9e461b8
      internalID: 588726727
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_16
      rect:
        serializedVersion: 2
        x: 256
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e2ab9bbac07b34f47ba246fcdd5bd70c
      internalID: 823893436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_17
      rect:
        serializedVersion: 2
        x: 512
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 18bb4e20363f94803b4cf6b697405eb9
      internalID: -1512030440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_18
      rect:
        serializedVersion: 2
        x: 768
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9f94230935cd74eedb707482b0a9a8e8
      internalID: 1929329644
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_19
      rect:
        serializedVersion: 2
        x: 1024
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b940d6d30934947249fb3902749af6e0
      internalID: 1833531500
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_20
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4a16e3ae8f3c847ae90d0a73e3c41b52
      internalID: 41271940
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_21
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e04a5df33821a4f128a49cb8c746cd1e
      internalID: 908021995
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_22
      rect:
        serializedVersion: 2
        x: 512
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b080efceda9244f97a51fff4a026b882
      internalID: -1742201997
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_23
      rect:
        serializedVersion: 2
        x: 768
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9837dc4f7e554438e935fe537e73ca92
      internalID: -1672581501
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_24
      rect:
        serializedVersion: 2
        x: 1024
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2fc45a3be915049ab8a5e6550fc0283a
      internalID: -1509728653
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_25
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 27f43584363e44d609a90b7f9b086653
      internalID: 565635832
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_26
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9f6daedfab0c347dba609fad4ef13e83
      internalID: -1177142980
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_27
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bfc7c9a1d9edf461c9a04dfce4a6c4cd
      internalID: 1536413472
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_28
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0aff47e28cce6468395b83577821ef67
      internalID: -1681978728
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thin_default_29
      rect:
        serializedVersion: 2
        x: 1024
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1ace60206a73c47ba991fc0f578cc242
      internalID: -180094620
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: b3bd3b2a3f362446fbe486bdf313a242
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      thin_default_0: -543733509
      thin_default_1: 1637635196
      thin_default_10: 1304389951
      thin_default_11: 7237954
      thin_default_12: -515425806
      thin_default_13: 284853848
      thin_default_14: 955299104
      thin_default_15: 588726727
      thin_default_16: 823893436
      thin_default_17: -1512030440
      thin_default_18: 1929329644
      thin_default_19: 1833531500
      thin_default_2: 1154087993
      thin_default_20: 41271940
      thin_default_21: 908021995
      thin_default_22: -1742201997
      thin_default_23: -1672581501
      thin_default_24: -1509728653
      thin_default_25: 565635832
      thin_default_26: -1177142980
      thin_default_27: 1536413472
      thin_default_28: -1681978728
      thin_default_29: -180094620
      thin_default_3: 1043700635
      thin_default_4: 1466820462
      thin_default_5: 1940972834
      thin_default_6: 60339863
      thin_default_7: 437715061
      thin_default_8: -376805070
      thin_default_9: 1796675843
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
