fileFormatVersion: 2
guid: b13814bfd7ec746a39f63cce2e754ca4
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 256
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: thick_default_0
      rect:
        serializedVersion: 2
        x: 0
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 71761dd2f443e4cf194801440b3c1c9b
      internalID: -2040307343
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_1
      rect:
        serializedVersion: 2
        x: 256
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 13b44aaa9c2aa4286bc1c11b99b89b61
      internalID: -278907789
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_2
      rect:
        serializedVersion: 2
        x: 512
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c2fe3670d77bc4816a82d3b1c94455b3
      internalID: 1158288755
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_3
      rect:
        serializedVersion: 2
        x: 768
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 459fb1e3c75db4ba9926243b5f55ced1
      internalID: 1018161976
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_4
      rect:
        serializedVersion: 2
        x: 1024
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 483d3a6a3400e4089b908940359621dd
      internalID: 1435147865
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_5
      rect:
        serializedVersion: 2
        x: 0
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 45c678024fe1240ada9b8e1c4ce5a980
      internalID: -1333388291
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_6
      rect:
        serializedVersion: 2
        x: 256
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2f856cf48239244b1bcd7eec814ecb7f
      internalID: 1532805975
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_7
      rect:
        serializedVersion: 2
        x: 512
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 730cf1e4e5d1a4bdb8fc3ff06433b361
      internalID: 1018785812
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_8
      rect:
        serializedVersion: 2
        x: 768
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 812dad736f226451abca391a323455a5
      internalID: -80276609
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_9
      rect:
        serializedVersion: 2
        x: 1024
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2285944f577e0498da90dd57b61a5b66
      internalID: -1915685955
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_10
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 19a861d9c60164b26bc9925d7a31f118
      internalID: 457632104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_11
      rect:
        serializedVersion: 2
        x: 256
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c26c355d8e05f49a6b39d7386e5b75a9
      internalID: 477747244
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_12
      rect:
        serializedVersion: 2
        x: 512
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a0e8e122380d1411595765250b32f0ec
      internalID: -38222968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_13
      rect:
        serializedVersion: 2
        x: 768
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7577d94afa51b4689a8521503e2b0ef3
      internalID: -67672742
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_14
      rect:
        serializedVersion: 2
        x: 1024
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 77f140fc393394967b10a4ae7cbaa585
      internalID: -1231961596
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_15
      rect:
        serializedVersion: 2
        x: 0
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d0b22475425e1417bbb5266fa38266f0
      internalID: -431042736
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_16
      rect:
        serializedVersion: 2
        x: 256
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bd0f69084d1994c01bc206a809b471f2
      internalID: 2017400950
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_17
      rect:
        serializedVersion: 2
        x: 512
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 033dc746494da4772b55219193a69a41
      internalID: -1686200361
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_18
      rect:
        serializedVersion: 2
        x: 768
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d5e9c47f8f05742a9b48fac4f4422037
      internalID: 2113779499
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_19
      rect:
        serializedVersion: 2
        x: 1024
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7b72c2313f43a4f1a8fd73ceeddd6ce5
      internalID: -1088757772
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_20
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e6ec3e26e52ae40de91f14e9e6852d18
      internalID: -1329062064
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_21
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7a71ddd332e47442987da3ae0a2064e1
      internalID: -1040626787
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_22
      rect:
        serializedVersion: 2
        x: 512
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1c3fce41d134648e38da25545b16cf0f
      internalID: 1044452930
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_23
      rect:
        serializedVersion: 2
        x: 768
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 69ec967cadf9244e1ab708ab8a6dec3e
      internalID: -2129402375
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_24
      rect:
        serializedVersion: 2
        x: 1024
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9876704bf0be3498ba05084212d7bb4a
      internalID: -1037793436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_25
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ee7b09a39aeef4ab085e6097e7b0bb4a
      internalID: 1075801065
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_26
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1eda1fb14c4f847cda148db18c2c00c7
      internalID: 1492520208
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_27
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b1cc47e8ba7a147cb9363b17587bade2
      internalID: -810955402
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_28
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 113a0ae2138e14a05ac7f2f1852d7ae6
      internalID: -1241292067
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: thick_default_29
      rect:
        serializedVersion: 2
        x: 1024
        y: 0
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d8bf5e3f3bcde47e08677052dacdfd76
      internalID: 129483783
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 618cb7e94b020429e94ead3f173d02e8
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      thick_default_0: -2040307343
      thick_default_1: -278907789
      thick_default_10: 457632104
      thick_default_11: 477747244
      thick_default_12: -38222968
      thick_default_13: -67672742
      thick_default_14: -1231961596
      thick_default_15: -431042736
      thick_default_16: 2017400950
      thick_default_17: -1686200361
      thick_default_18: 2113779499
      thick_default_19: -1088757772
      thick_default_2: 1158288755
      thick_default_20: -1329062064
      thick_default_21: -1040626787
      thick_default_22: 1044452930
      thick_default_23: -2129402375
      thick_default_24: -1037793436
      thick_default_25: 1075801065
      thick_default_26: 1492520208
      thick_default_27: -810955402
      thick_default_28: -1241292067
      thick_default_29: 129483783
      thick_default_3: 1018161976
      thick_default_4: 1435147865
      thick_default_5: -1333388291
      thick_default_6: 1532805975
      thick_default_7: 1018785812
      thick_default_8: -80276609
      thick_default_9: -1915685955
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
