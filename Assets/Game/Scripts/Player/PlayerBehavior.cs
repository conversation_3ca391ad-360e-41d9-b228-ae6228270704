using UncleChenGames.Easing;
using UncleChenGames.Extensions;
using Uncle<PERSON>henGames.Upgrades;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using UnityEngine;
using UnityEngine.Events;

namespace UncleChenGames
{
    /// <summary>
    /// 玩家行为控制器，管理玩家的移动、属性、碰撞检测、受伤和复活等核心功能
    /// </summary>
    public class PlayerBehavior : MonoBehaviour
    {
        private static readonly int DEATH_HASH = "Death".GetHashCode(); // 死亡音效哈希值
        private static readonly int REVIVE_HASH = "Revive".GetHashCode(); // 复活音效哈希值
        private static readonly int RECEIVING_DAMAGE_HASH = "Receiving Damage".GetHashCode(); // 受伤音效哈希值

        private static PlayerBehavior instance; // 单例实例
        /// <summary>
        /// 玩家实例
        /// </summary>
        public static PlayerBehavior Player { get => instance; }

        [SerializeField] CharactersDatabase charactersDatabase; // 角色数据库

        [Header("Stats")]
        [SerializeField, Min(0.01f)] float speed = 2; // 基础移动速度
        [SerializeField, Min(0.1f)] float defaultMagnetRadius = 0.75f; // 默认磁铁半径
        [SerializeField, Min(1f)] float xpMultiplier = 1; // 经验值倍率
        [SerializeField, Range(0.1f, 1f)] float cooldownMultiplier = 1; // 冷却时间倍率
        [SerializeField, Range(0, 100)] int initialDamageReductionPercent = 0; // 初始伤害减免百分比
        [SerializeField, Min(1f)] float initialProjectileSpeedMultiplier = 1; // 初始弹丸速度倍率
        [SerializeField, Min(1f)] float initialSizeMultiplier = 1f; // 初始尺寸倍率
        [SerializeField, Min(1f)] float initialDurationMultiplier = 1f; // 初始持续时间倍率
        [SerializeField, Min(1f)] float initialGoldMultiplier = 1; // 初始金币倍率

        [Header("References")]
        [SerializeField] HealthbarBehavior healthbar; // 血条控制器
        [SerializeField] Transform centerPoint; // 中心点Transform
        [SerializeField] PlayerEnemyCollisionHelper collisionHelper; // 碰撞检测辅助器

        /// <summary>
        /// 玩家中心点Transform
        /// </summary>
        public static Transform CenterTransform { get => instance.centerPoint; }
        /// <summary>
        /// 玩家中心点位置
        /// </summary>
        public static Vector2 CenterPosition { get => instance.centerPoint.position; }

        [Header("Death and Revive")]
        [SerializeField] ParticleSystem reviveParticle; // 复活粒子特效

        [Space]
        [SerializeField] SpriteRenderer reviveBackgroundSpriteRenderer; // 复活背景精灵渲染器
        [SerializeField, Range(0, 1)] float reviveBackgroundAlpha; // 复活背景透明度
        [SerializeField, Range(0, 1)] float reviveBackgroundSpawnDelay; // 复活背景出现延迟
        [SerializeField, Range(0, 1)] float reviveBackgroundHideDelay; // 复活背景隐藏延迟

        [Space]
        [SerializeField] SpriteRenderer reviveBottomSpriteRenderer; // 复活底部精灵渲染器
        [SerializeField, Range(0, 1)] float reviveBottomAlpha; // 复活底部透明度
        [SerializeField, Range(0, 1)] float reviveBottomSpawnDelay; // 复活底部出现延迟
        [SerializeField, Range(0, 1)] float reviveBottomHideDelay; // 复活底部隐藏延迟

        [Header("Other")]
        [SerializeField] Vector2 borderOffset = new Vector2(0.5f, 0.5f); // 地图边界偏移（防止精灵与边界重叠）
        [SerializeField] Vector2 fenceOffset; // Boss围栏偏移
        [SerializeField] Color hitColor; // 受击颜色
        [SerializeField] float enemyInsideDamageInterval = 2f; // 敌人内部伤害间隔

        /// <summary>
        /// 玩家死亡事件
        /// </summary>
        public event UnityAction onPlayerDied;

        /// <summary>
        /// 当前伤害值
        /// </summary>
        public float Damage { get; private set; }
        /// <summary>
        /// 磁铁半径的平方（用于性能优化）
        /// </summary>
        public float MagnetRadiusSqr { get; private set; }
        /// <summary>
        /// 当前移动速度
        /// </summary>
        public float Speed { get; private set; }

        /// <summary>
        /// 经验值倍率
        /// </summary>
        public float XPMultiplier { get; private set; }
        /// <summary>
        /// 冷却时间倍率
        /// </summary>
        public float CooldownMultiplier { get; private set; }
        /// <summary>
        /// 伤害减免倍率
        /// </summary>
        public float DamageReductionMultiplier { get; private set; }
        /// <summary>
        /// 弹丸速度倍率
        /// </summary>
        public float ProjectileSpeedMultiplier { get; private set; }
        /// <summary>
        /// 尺寸倍率
        /// </summary>
        public float SizeMultiplier { get; private set; }
        /// <summary>
        /// 持续时间倍率
        /// </summary>
        public float DurationMultiplier { get; private set; }
        /// <summary>
        /// 金币倍率
        /// </summary>
        public float GoldMultiplier { get; private set; }

        /// <summary>
        /// 朝向方向
        /// </summary>
        public Vector2 LookDirection { get; private set; }
        /// <summary>
        /// 是否允许移动
        /// </summary>
        public bool IsMovingAlowed { get; set; }

        private bool invincible = false; // 是否无敌

        private List<EnemyBehavior> enemiesInside = new List<EnemyBehavior>(); // 内部敌人列表

        private CharactersSave charactersSave; // 角色存档数据
        /// <summary>
        /// 角色数据
        /// </summary>
        public CharacterData Data { get; set; }
        /// <summary>
        /// 角色行为组件
        /// </summary>
        private CharacterBehavior Character { get; set; }

        /// <summary>
        /// Unity生命周期方法，在对象创建时调用，初始化玩家数据和属性
        /// </summary>
        private void Awake()
        {
            // 单例安全检查
            if (instance != null && instance != this)
            {
                Debug.LogError($"[PlayerBehavior] 检测到重复的玩家实例！当前对象: {gameObject.name} 将被销毁。已存在的实例: {instance.gameObject.name}");
                Destroy(gameObject);
                return;
            }

            // 获取角色存档数据
            charactersSave = GameController.SaveManager.GetSave<CharactersSave>("Characters");
            // 根据选中的角色ID获取角色数据
            Data = charactersDatabase.GetCharacterData(charactersSave.SelectedCharacterId);

            // 实例化角色预制体并获取CharacterBehavior组件
            Character = Instantiate(Data.Prefab).GetComponent<CharacterBehavior>();
            // 设置角色为当前对象的子物体
            Character.transform.SetParent(transform);
            // 重置角色的本地变换
            Character.transform.ResetLocal();

            // 设置单例实例
            instance = this;
            // 初始化血条
            healthbar.Init(Data.BaseHP);
            // 设置血条满血时自动隐藏
            healthbar.SetAutoHideWhenMax(true);
            // 设置血量变化时自动显示
            healthbar.SetAutoShowOnChanged(true);

            // 初始化所有属性倍率为基础值
            RecalculateMagnetRadius(1);
            RecalculateMoveSpeed(1);
            RecalculateDamage(1);
            RecalculateMaxHP(1);
            RecalculateXPMuliplier(1);
            RecalculateCooldownMuliplier(1);
            RecalculateDamageReduction(0);
            RecalculateProjectileSpeedMultiplier(1f);
            RecalculateSizeMultiplier(1f);
            RecalculateDurationMultiplier(1);
            RecalculateGoldMultiplier(1);

            // 设置初始朝向为右方
            LookDirection = Vector2.right;

            // 允许移动
            IsMovingAlowed = true;
        }

        /// <summary>
        /// Unity生命周期方法，每帧更新，处理移动、敌人伤害和输入
        /// </summary>
        private void Update()
        {
            // 如果血量为零，不执行任何逻辑
            if (healthbar.IsZero) return;

            // 处理内部敌人的持续伤害
            foreach (var enemy in enemiesInside)
            {
                // 检查是否到达下次伤害时间
                if (Time.time - enemy.LastTimeDamagedPlayer > enemyInsideDamageInterval)
                {
                    // 对玩家造成伤害
                    TakeDamage(enemy.GetDamage());
                    // 更新敌人上次伤害玩家的时间
                    enemy.LastTimeDamagedPlayer = Time.time;
                }
            }

            // 如果不允许移动，返回
            if (!IsMovingAlowed) return;

            // 获取输入值
            var input = GameController.InputManager.MovementValue;

            // 计算摇杆力度
            float joysticPower = input.magnitude;
            // 设置角色动画速度
            Character.SetSpeed(joysticPower);

            // 如果有输入且游戏未暂停
            if (!Mathf.Approximately(joysticPower, 0) && Time.timeScale > 0)
            {
                // 计算本帧移动量
                var frameMovement = input * Time.deltaTime * Speed;

                // 根据是否有Boss围栏来选择使用哪个偏移量
                Vector2 currentOffset = StageController.FieldManager.Fence != null ? fenceOffset : borderOffset;
                
                // 验证并执行X轴移动
                if (StageController.FieldManager.ValidatePosition(transform.position + Vector3.right * frameMovement.x, currentOffset))
                {
                    transform.position += Vector3.right * frameMovement.x;
                }

                // 验证并执行Y轴移动
                if (StageController.FieldManager.ValidatePosition(transform.position + Vector3.up * frameMovement.y, currentOffset))
                {
                    transform.position += Vector3.up * frameMovement.y;
                }

                // 重置碰撞检测器位置
                collisionHelper.transform.localPosition = Vector3.zero;

                // 根据输入方向设置角色缩放（左右翻转）
                Character.SetLocalScale(new Vector3(input.x > 0 ? 1 : -1, 1, 1));

                // 更新朝向方向
                LookDirection = input.normalized;
            }
        }

        /// <summary>
        /// 检查目标是否在磁铁范围内（使用内联优化）
        /// </summary>
        /// <param name="target">目标Transform</param>
        /// <returns>是否在磁铁范围内</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool IsInsideMagnetRadius(Transform target)
        {
            // 使用平方距离比较避免开方运算
            return (transform.position - target.position).sqrMagnitude <= MagnetRadiusSqr;
        }

        /// <summary>
        /// 重新计算磁铁半径
        /// </summary>
        /// <param name="magnetRadiusMultiplier">磁铁半径倍率</param>
        public void RecalculateMagnetRadius(float magnetRadiusMultiplier)
        {
            // 计算磁铁半径的平方，用于性能优化
            MagnetRadiusSqr = Mathf.Pow(defaultMagnetRadius * magnetRadiusMultiplier, 2);
        }

        /// <summary>
        /// 重新计算移动速度
        /// </summary>
        /// <param name="moveSpeedMultiplier">移动速度倍率</param>
        public void RecalculateMoveSpeed(float moveSpeedMultiplier)
        {
            Speed = speed * moveSpeedMultiplier;
        }

        /// <summary>
        /// 重新计算伤害值
        /// </summary>
        /// <param name="damageMultiplier">伤害倍率</param>
        public void RecalculateDamage(float damageMultiplier)
        {
            // 基础伤害乘以倍率
            Damage = Data.BaseDamage * damageMultiplier;
            // 如果有伤害升级，应用升级效果
            if (GameController.UpgradesManager.IsUpgradeAquired(UpgradeType.Damage))
            {
                Damage *= GameController.UpgradesManager.GetUpgadeValue(UpgradeType.Damage);
            }
        }

        /// <summary>
        /// 重新计算最大生命值
        /// </summary>
        /// <param name="maxHPMultiplier">最大生命值倍率</param>
        public void RecalculateMaxHP(float maxHPMultiplier)
        {
            // 获取生命值升级值
            var upgradeValue = GameController.UpgradesManager.GetUpgadeValue(UpgradeType.Health);
            // 应用基础生命值、升级值和倍率
            healthbar.ChangeMaxHP((Data.BaseHP + upgradeValue) * maxHPMultiplier);
        }

        /// <summary>
        /// 重新计算经验值倍率
        /// </summary>
        /// <param name="xpMultiplier">经验值倍率</param>
        public void RecalculateXPMuliplier(float xpMultiplier)
        {
            XPMultiplier = this.xpMultiplier * xpMultiplier;
        }

        /// <summary>
        /// 重新计算冷却时间倍率
        /// </summary>
        /// <param name="cooldownMultiplier">冷却时间倍率</param>
        public void RecalculateCooldownMuliplier(float cooldownMultiplier)
        {
            CooldownMultiplier = this.cooldownMultiplier * cooldownMultiplier;
        }

        /// <summary>
        /// 重新计算伤害减免倍率
        /// </summary>
        /// <param name="damageReductionPercent">伤害减免百分比</param>
        public void RecalculateDamageReduction(float damageReductionPercent)
        {
            // 计算伤害减免倍率：(100% - 初始减免% - 额外减免%) / 100%
            DamageReductionMultiplier = (100f - initialDamageReductionPercent - damageReductionPercent) / 100f;

            // 如果有护甲升级，应用升级效果
            if (GameController.UpgradesManager.IsUpgradeAquired(UpgradeType.Armor))
            {
                DamageReductionMultiplier *= GameController.UpgradesManager.GetUpgadeValue(UpgradeType.Armor);
            }
        }

        /// <summary>
        /// 重新计算弹丸速度倍率
        /// </summary>
        /// <param name="projectileSpeedMultiplier">弹丸速度倍率</param>
        public void RecalculateProjectileSpeedMultiplier(float projectileSpeedMultiplier)
        {
            ProjectileSpeedMultiplier = initialProjectileSpeedMultiplier * projectileSpeedMultiplier;
        }

        /// <summary>
        /// 重新计算尺寸倍率
        /// </summary>
        /// <param name="sizeMultiplier">尺寸倍率</param>
        public void RecalculateSizeMultiplier(float sizeMultiplier)
        {
            SizeMultiplier = initialSizeMultiplier * sizeMultiplier;
        }

        /// <summary>
        /// 重新计算持续时间倍率
        /// </summary>
        /// <param name="durationMultiplier">持续时间倍率</param>
        public void RecalculateDurationMultiplier(float durationMultiplier)
        {
            DurationMultiplier = initialDurationMultiplier * durationMultiplier;
        }

        /// <summary>
        /// 重新计算金币倍率
        /// </summary>
        /// <param name="goldMultiplier">金币倍率</param>
        public void RecalculateGoldMultiplier(float goldMultiplier)
        {
            GoldMultiplier = initialGoldMultiplier * goldMultiplier;
        }

        /// <summary>
        /// 按百分比恢复生命值
        /// </summary>
        /// <param name="hpPercent">恢复的生命值百分比</param>
        public void RestoreHP(float hpPercent)
        {
            healthbar.AddPercentage(hpPercent);
        }

        /// <summary>
        /// 恢复指定数值的生命值（包含治疗升级加成）
        /// </summary>
        /// <param name="hp">恢复的生命值</param>
        public void Heal(float hp)
        {
            // 恢复生命值并加上治疗升级的额外效果
            healthbar.AddHP(hp + GameController.UpgradesManager.GetUpgadeValue(UpgradeType.Healing));
        }

        /// <summary>
        /// 玩家复活逻辑
        /// </summary>
        public void Revive()
        {
            // 播放复活动画
            Character.PlayReviveAnimation();
            // 播放复活粒子特效
            reviveParticle.Play();

            // 设置无敌状态
            invincible = true;
            // 禁止移动
            IsMovingAlowed = false;
            // 重置生命值为满血
            healthbar.ResetHP(1f);

            // 设置角色渲染层级到最前
            Character.SetSortingOrder(102);

            // 淡出复活背景精灵并在完成后隐藏
            reviveBackgroundSpriteRenderer.DoAlpha(0f, 0.3f, reviveBottomHideDelay).SetUnscaledTime(true).SetOnFinish(() => reviveBackgroundSpriteRenderer.gameObject.SetActive(false));
            // 淡出复活底部精灵并在完成后隐藏
            reviveBottomSpriteRenderer.DoAlpha(0f, 0.3f, reviveBottomHideDelay).SetUnscaledTime(true).SetOnFinish(() => reviveBottomSpriteRenderer.gameObject.SetActive(false));

            // 播放复活音效
            GameController.AudioManager.PlaySound(REVIVE_HASH);
            // 1秒后恢复移动能力和正常渲染层级
            EasingManager.DoAfter(1f, () =>
            {
                IsMovingAlowed = true;
                Character.SetSortingOrder(0);
            });

            // 3秒后取消无敌状态
            EasingManager.DoAfter(3, () => invincible = false);
        }

        /// <summary>
        /// 检查触发器进入事件（由碰撞辅助器调用）
        /// </summary>
        /// <param name="collision">碰撞的Collider2D</param>
        public void CheckTriggerEnter2D(Collider2D collision)
        {
            // 检查是否为敌人层级（Layer 7）
            if (collision.gameObject.layer == 7)
            {
                // 如果处于无敌状态，忽略碰撞
                if (invincible) return;

                // 尝试获取敌人组件
                var enemy = collision.GetComponent<EnemyBehavior>();

                if (enemy != null)
                {
                    // 将敌人添加到内部敌人列表
                    enemiesInside.Add(enemy);
                    // 记录敌人上次伤害玩家的时间
                    enemy.LastTimeDamagedPlayer = Time.time;

                    // 订阅敌人死亡事件
                    enemy.onEnemyDied += OnEnemyDied;
                    // 立即造成伤害
                    TakeDamage(enemy.GetDamage());
                }
            }
            else
            {
                // 处理敌人弹丸碰撞
                if (invincible) return;

                // 尝试获取敌人弹丸组件
                var projectile = collision.GetComponent<SimpleEnemyProjectileBehavior>();
                if (projectile != null)
                {
                    // 造成弹丸伤害
                    TakeDamage(projectile.Damage);
                }
            }
        }

        /// <summary>
        /// 检查触发器退出事件（由碰撞辅助器调用）
        /// </summary>
        /// <param name="collision">碰撞的Collider2D</param>
        public void CheckTriggerExit2D(Collider2D collision)
        {
            // 检查是否为敌人层级（Layer 7）
            if (collision.gameObject.layer == 7)
            {
                if (invincible) return;

                // 尝试获取敌人组件
                var enemy = collision.GetComponent<EnemyBehavior>();

                if (enemy != null)
                {
                    // 从内部敌人列表中移除
                    enemiesInside.Remove(enemy);
                    // 取消订阅敌人死亡事件
                    enemy.onEnemyDied -= OnEnemyDied;
                }
            }
        }

        /// <summary>
        /// 敌人死亡时的回调方法
        /// </summary>
        /// <param name="enemy">死亡的敌人</param>
        private void OnEnemyDied(EnemyBehavior enemy)
        {
            // 取消订阅敌人死亡事件
            enemy.onEnemyDied -= OnEnemyDied;
            // 从内部敌人列表中移除
            enemiesInside.Remove(enemy);
        }

        private float lastTimeVibrated = 0f; // 上次震动时间

        /// <summary>
        /// 玩家受到伤害的处理方法
        /// </summary>
        /// <param name="damage">伤害值</param>
        public void TakeDamage(float damage)
        {
            // 如果处于无敌状态或已死亡，忽略伤害
            if (invincible || healthbar.IsZero) return;

            // 扣除生命值（应用伤害减免）
            healthbar.Subtract(damage * DamageReductionMultiplier);

            // 播放受击闪烁效果
            Character.FlashHit();

            // 检查是否死亡
            if (healthbar.IsZero)
            {
                // 播放死亡动画
                Character.PlayDefeatAnimation();
                // 设置角色渲染层级到最前
                Character.SetSortingOrder(102);

                // 激活并淡入复活背景精灵
                reviveBackgroundSpriteRenderer.gameObject.SetActive(true);
                reviveBackgroundSpriteRenderer.DoAlpha(reviveBackgroundAlpha, 0.3f, reviveBackgroundSpawnDelay).SetUnscaledTime(true);
                // 设置复活背景位置到玩家位置
                reviveBackgroundSpriteRenderer.transform.position = transform.position.SetZ(reviveBackgroundSpriteRenderer.transform.position.z);

                // 激活并淡入复活底部精灵
                reviveBottomSpriteRenderer.gameObject.SetActive(true);
                reviveBottomSpriteRenderer.DoAlpha(reviveBottomAlpha, 0.3f, reviveBottomSpawnDelay).SetUnscaledTime(true);

                // 播放死亡音效
                GameController.AudioManager.PlaySound(DEATH_HASH);

                // 延迟0.5秒后触发玩家死亡事件
                EasingManager.DoAfter(0.5f, () =>
                {
                    onPlayerDied?.Invoke();
                }).SetUnscaledTime(true);

                // 强烈震动反馈
                GameController.VibrationManager.StrongVibration();
            }
            else
            {
                // 受伤但未死亡的处理
                // 限制震动频率（每0.05秒最多一次）
                if (Time.time - lastTimeVibrated > 0.05f)
                {
                    // 轻微震动反馈
                    GameController.VibrationManager.LightVibration();
                    lastTimeVibrated = Time.time;
                }

                // 播放受伤音效
                GameController.AudioManager.PlaySound(RECEIVING_DAMAGE_HASH);
            }
        }

        /// <summary>
        /// Unity生命周期方法，在对象销毁时清理单例引用
        /// </summary>
        private void OnDestroy()
        {
            // 只有当前实例才清理引用
            if (instance == this)
            {
                instance = null;
            }
        }
    }
}