using UncleChenGames.Extensions;
using System.Collections.Generic;
using UnityEngine;

namespace UncleChenGames
{
    /// <summary>
    /// 矩形场地行为，管理一个具有固定边界的矩形场地
    /// </summary>
    public class RectFieldBehavior : AbstractFieldBehavior
    {
        // 配置参数
        private const int DEFAULT_COLUMNS = 2; // 默认列数
        private const int DEFAULT_ROWS = 2;    // 默认行数

        // 边界尺寸数据结构
        private struct BorderSizes
        {
            public Vector2 topSize;        // 上边界尺寸
            public Vector2 bottomSize;     // 下边界尺寸
            public Vector2 leftSize;       // 左边界尺寸
            public Vector2 rightSize;      // 右边界尺寸
            public Vector2 topLeftSize;    // 左上角尺寸
            public Vector2 topRightSize;   // 右上角尺寸
            public Vector2 bottomLeftSize; // 左下角尺寸
            public Vector2 bottomRightSize;// 右下角尺寸
        }

        // 场地数据
        private List<StageChunkBehavior> chunks = new List<StageChunkBehavior>(); // 场地块列表
        private List<Transform> borders = new List<Transform>(); // 边界列表
        private Vector2 totalSize; // 总地图大小
        private Vector2 chunkSize; // 单个块的大小
        private int columnsCount = DEFAULT_COLUMNS; // 列数
        private int rowsCount = DEFAULT_ROWS;    // 行数
        private BorderSizes borderSizes; // 边界尺寸存储

        /// <summary>
        /// 初始化矩形场地
        /// </summary>
        /// <param name="stageFieldData">关卡场地数据</param>
        /// <param name="spawnProp">是否生成道具</param>
        public override void Init(StageFieldData stageFieldData, bool spawnProp)
        {
            // 调用基类的初始化方法
            base.Init(stageFieldData, spawnProp);

            // 初始化场地参数
            InitializeFieldParameters(stageFieldData);

            // 创建场地块
            CreateFieldChunks(stageFieldData);

            // 创建边界
            CreateBorders(stageFieldData);

            // 创建角落
            CreateCorners(stageFieldData);

            // 在所有场地块上生成道具
            SpawnPropsOnAllChunks();
        }

        /// <summary>
        /// 初始化场地参数
        /// </summary>
        private void InitializeFieldParameters(StageFieldData stageFieldData)
        {
            // 获取单个块的大小
            chunkSize = GetChunkSize(stageFieldData.BackgroundPrefab);

            // 计算总地图大小
            totalSize = new Vector2(chunkSize.x * columnsCount, chunkSize.y * rowsCount);
        }

        /// <summary>
        /// 获取场地块大小
        /// </summary>
        private Vector2 GetChunkSize(GameObject prefab)
        {
            var tempChunk = Object.Instantiate(prefab).GetComponent<StageChunkBehavior>();
            var size = tempChunk.Size;
            Object.Destroy(tempChunk.gameObject);
            return size;
        }

        /// <summary>
        /// 获取预制体的精灵尺寸
        /// </summary>
        /// <param name="prefab">预制体</param>
        /// <returns>预制体的尺寸，如果获取失败则返回chunkSize作为默认值</returns>
        private Vector2 GetPrefabSize(GameObject prefab)
        {
            // 如果预制体为空，返回默认值
            if (prefab == null) return chunkSize;
            
            // 实例化预制体以获取尺寸
            var temp = Object.Instantiate(prefab);
            
            // 先尝试从根物体获取SpriteRenderer
            var spriteRenderer = temp.GetComponent<SpriteRenderer>();
            
            // 如果根物体没有，尝试从子物体获取
            if (spriteRenderer == null)
            {
                spriteRenderer = temp.GetComponentInChildren<SpriteRenderer>();
            }
            
            Vector2 size;
            if (spriteRenderer != null && spriteRenderer.sprite != null)
            {
                // 获取精灵的实际尺寸
                size = spriteRenderer.size;
            }
            else
            {
                // 如果没有SpriteRenderer，尝试获取Collider2D的尺寸（先根物体，后子物体）
                var collider = temp.GetComponent<Collider2D>();
                if (collider == null)
                {
                    collider = temp.GetComponentInChildren<Collider2D>();
                }
                
                if (collider != null)
                {
                    size = collider.bounds.size;
                }
                else
                {
                    // 如果都没有，使用默认值并记录警告
                    Debug.LogWarning($"边界预制体 {prefab.name} 及其子物体都没有SpriteRenderer或Collider2D组件，使用默认尺寸");
                    size = chunkSize;
                }
            }
            
            // 销毁临时对象
            Object.Destroy(temp);
            return size;
        }

        /// <summary>
        /// 创建场地块
        /// </summary>
        private void CreateFieldChunks(StageFieldData stageFieldData)
        {
            for (int row = 0; row < rowsCount; row++)
            {
                for (int col = 0; col < columnsCount; col++)
                {
                    var chunk = Object.Instantiate(stageFieldData.BackgroundPrefab).GetComponent<StageChunkBehavior>();
                    chunk.transform.position = GetChunkPosition(row, col);
                    chunk.transform.rotation = Quaternion.identity;
                    chunk.transform.localScale = Vector3.one;
                    chunks.Add(chunk);
                }
            }
        }

        /// <summary>
        /// 计算场地块位置
        /// </summary>
        private Vector3 GetChunkPosition(int row, int col)
        {
            float xPos = -totalSize.x / 2 + chunkSize.x / 2 + col * chunkSize.x;
            float yPos = totalSize.y / 2 - chunkSize.y / 2 - row * chunkSize.y;
            return new Vector3(xPos, yPos, 0);
        }

        /// <summary>
        /// 创建边界
        /// </summary>
        private void CreateBorders(StageFieldData stageFieldData)
        {
            // 获取所有边界的实际尺寸
            borderSizes.topSize = GetPrefabSize(stageFieldData.TopPrefab);
            borderSizes.bottomSize = GetPrefabSize(stageFieldData.BottomPrefab);
            borderSizes.leftSize = GetPrefabSize(stageFieldData.LeftPrefab);
            borderSizes.rightSize = GetPrefabSize(stageFieldData.RightPrefab);

            // 调试日志：显示边界尺寸信息
            if (Application.isEditor)
            {
                // 检查边界尺寸是否与chunkSize匹配
                if (borderSizes.topSize != chunkSize && stageFieldData.TopPrefab != null)
                    Debug.Log($"上边界尺寸 ({borderSizes.topSize}) 与地图块尺寸 ({chunkSize}) 不匹配");
                if (borderSizes.bottomSize != chunkSize && stageFieldData.BottomPrefab != null)
                    Debug.Log($"下边界尺寸 ({borderSizes.bottomSize}) 与地图块尺寸 ({chunkSize}) 不匹配");
                if (borderSizes.leftSize != chunkSize && stageFieldData.LeftPrefab != null)
                    Debug.Log($"左边界尺寸 ({borderSizes.leftSize}) 与地图块尺寸 ({chunkSize}) 不匹配");
                if (borderSizes.rightSize != chunkSize && stageFieldData.RightPrefab != null)
                    Debug.Log($"右边界尺寸 ({borderSizes.rightSize}) 与地图块尺寸 ({chunkSize}) 不匹配");
            }

            // 使用实际边界尺寸计算固定坐标
            // 创建顶部边界
            float topY = totalSize.y / 2 + borderSizes.topSize.y / 2;
            CreateBorderLine(stageFieldData.TopPrefab, borderSizes.topSize, columnsCount, true, topY);

            // 创建底部边界
            float bottomY = -totalSize.y / 2 - borderSizes.bottomSize.y / 2;
            CreateBorderLine(stageFieldData.BottomPrefab, borderSizes.bottomSize, columnsCount, true, bottomY);

            // 创建左侧边界
            float leftX = -totalSize.x / 2 - borderSizes.leftSize.x / 2;
            CreateBorderLine(stageFieldData.LeftPrefab, borderSizes.leftSize, rowsCount, false, leftX);

            // 创建右侧边界
            float rightX = totalSize.x / 2 + borderSizes.rightSize.x / 2;
            CreateBorderLine(stageFieldData.RightPrefab, borderSizes.rightSize, rowsCount, false, rightX);
        }

        /// <summary>
        /// 创建一条边界线
        /// </summary>
        /// <param name="prefab">边界预制体</param>
        /// <param name="borderSize">边界实际尺寸</param>
        /// <param name="count">边界块数量</param>
        /// <param name="isHorizontal">是否为水平边界</param>
        /// <param name="fixedCoord">固定坐标（水平边界为y坐标，垂直边界为x坐标）</param>
        private void CreateBorderLine(GameObject prefab, Vector2 borderSize, int count, bool isHorizontal, float fixedCoord)
        {
            if (prefab == null) return;

            // 计算需要的边界段数量
            if (isHorizontal)
            {
                // 水平边界：根据边界宽度计算需要多少段来覆盖整个地图宽度
                count = Mathf.CeilToInt(totalSize.x / borderSize.x);
            }
            else
            {
                // 垂直边界：根据边界高度计算需要多少段来覆盖整个地图高度
                count = Mathf.CeilToInt(totalSize.y / borderSize.y);
            }

            for (int i = 0; i < count; i++)
            {
                var border = Object.Instantiate(prefab).GetComponent<Transform>();

                if (isHorizontal)
                {
                    // 使用实际边界宽度计算每段的位置
                    float xPos = -totalSize.x / 2 + borderSize.x / 2 + i * borderSize.x;
                    border.transform.position = new Vector3(xPos, fixedCoord, 0);
                }
                else
                {
                    // 使用实际边界高度计算每段的位置
                    float yPos = totalSize.y / 2 - borderSize.y / 2 - i * borderSize.y;
                    border.transform.position = new Vector3(fixedCoord, yPos, 0);
                }

                borders.Add(border);
            }
        }

        /// <summary>
        /// 创建角落
        /// </summary>
        private void CreateCorners(StageFieldData stageFieldData)
        {
            // 获取角落预制体的实际尺寸
            borderSizes.topLeftSize = GetPrefabSize(stageFieldData.TopLeftPrefab);
            borderSizes.topRightSize = GetPrefabSize(stageFieldData.TopRightPrefab);
            borderSizes.bottomLeftSize = GetPrefabSize(stageFieldData.BottomLeftPrefab);
            borderSizes.bottomRightSize = GetPrefabSize(stageFieldData.BottomRightPrefab);

            // 使用相邻边界的实际尺寸计算角落位置
            // 左上角：位于左边界和上边界的交汇处
            float topLeftX = -totalSize.x / 2 - borderSizes.leftSize.x / 2;
            float topLeftY = totalSize.y / 2 + borderSizes.topSize.y / 2;
            CreateCorner(stageFieldData.TopLeftPrefab, topLeftX, topLeftY);

            // 右上角：位于右边界和上边界的交汇处
            float topRightX = totalSize.x / 2 + borderSizes.rightSize.x / 2;
            float topRightY = totalSize.y / 2 + borderSizes.topSize.y / 2;
            CreateCorner(stageFieldData.TopRightPrefab, topRightX, topRightY);

            // 左下角：位于左边界和下边界的交汇处
            float bottomLeftX = -totalSize.x / 2 - borderSizes.leftSize.x / 2;
            float bottomLeftY = -totalSize.y / 2 - borderSizes.bottomSize.y / 2;
            CreateCorner(stageFieldData.BottomLeftPrefab, bottomLeftX, bottomLeftY);

            // 右下角：位于右边界和下边界的交汇处
            float bottomRightX = totalSize.x / 2 + borderSizes.rightSize.x / 2;
            float bottomRightY = -totalSize.y / 2 - borderSizes.bottomSize.y / 2;
            CreateCorner(stageFieldData.BottomRightPrefab, bottomRightX, bottomRightY);
        }

        /// <summary>
        /// 创建单个角落
        /// </summary>
        private void CreateCorner(GameObject prefab, float x, float y)
        {
            if (prefab == null) return;

            var corner = Object.Instantiate(prefab).GetComponent<Transform>();
            corner.transform.position = new Vector2(x, y);
            borders.Add(corner);
        }

        /// <summary>
        /// 在所有场地块上生成道具
        /// </summary>
        private void SpawnPropsOnAllChunks()
        {
            foreach (var chunk in chunks)
            {
                SpawnProp(chunk);
            }
        }

        /// <summary>
        /// 每帧更新（矩形场地无需更新操作）
        /// </summary>
        public override void Update()
        {

        }

        /// <summary>
        /// 验证位置是否在矩形边界内
        /// </summary>
        public override bool ValidatePosition(Vector2 position)
        {
            // 检查位置是否超出左右边界（基于总地图大小）
            if (position.x > totalSize.x / 2) return false;
            if (position.x < -totalSize.x / 2) return false;

            // 检查位置是否超出上下边界（基于总地图大小）
            if (position.y > totalSize.y / 2) return false;
            if (position.y < -totalSize.y / 2) return false;

            return true;
        }

        /// <summary>
        /// 在矩形边界上获取一个随机位置
        /// </summary>
        public override Vector2 GetRandomPositionOnBorder()
        {
            // 生成一个单位圆内的随机点，然后将其投影到矩形边界上
            Vector2 randomPoint = Random.insideUnitCircle.normalized * Mathf.Max(totalSize.x, totalSize.y);

            // 将点限制在矩形的边界内（基于总地图大小）
            if (randomPoint.x > totalSize.x / 2) randomPoint.x = totalSize.x / 2;
            if (randomPoint.x < -totalSize.x / 2) randomPoint.x = -totalSize.x / 2;

            if (randomPoint.y > totalSize.y / 2) randomPoint.y = totalSize.y / 2;
            if (randomPoint.y < -totalSize.y / 2) randomPoint.y = -totalSize.y / 2;

            // 返回边界上的点（地图中心在原点）
            return randomPoint;
        }

        /// <summary>
        /// 在矩形边界内获取一个随机位置
        /// </summary>
        public override Vector2 GetRandomPositionInside()
        {
            // 在矩形边界内生成随机点
            float x = Random.Range(-totalSize.x / 2, totalSize.x / 2);
            float y = Random.Range(-totalSize.y / 2, totalSize.y / 2);
            return new Vector2(x, y);
        }

        /// <summary>
        /// 获取敌人生成位置（矩形场地专用）
        /// </summary>
        /// <param name="preferOutsideCamera">是否优先在摄像机外生成</param>
        /// <returns>敌人生成位置</returns>
        public override Vector2 GetEnemySpawnPosition(bool preferOutsideCamera = true)
        {
            // 对于矩形场地，直接在边界内随机生成敌人
            // 不需要考虑摄像机位置，确保敌人始终在边界内
            return GetRandomPositionInside();
        }

        /// <summary>
        /// 获取Boss生成位置
        /// </summary>
        public override Vector2 GetBossSpawnPosition(BossFenceBehavior fence, Vector2 offset)
        {
            // 根据不同的围栏类型调整Boss围栏尺寸（基于总地图大小）
            if (fence is CircleFenceBehavior circleFence)
            {
                // 计算矩形对角线长度并设置为圆形围栏的半径
                float diagonal = Mathf.Sqrt(Mathf.Pow(totalSize.x / 2, 2) + Mathf.Pow(totalSize.y / 2, 2)) * 1.1f;
                circleFence.SetRadiusOverride(diagonal);
            }
            else if (fence is RectFenceBehavior rectFence)
            {
                // 设置矩形围栏的尺寸以匹配总地图大小
                rectFence.SetSizeOverride(totalSize.x * 1.1f, totalSize.y * 1.1f);
            }

            // 在矩形场地中心生成Boss
            return Vector2.zero;
        }

        /// <summary>
        /// 检查点是否在右边界之外
        /// </summary>
        public override bool IsPointOutsideRight(Vector2 point, out float distance)
        {
            float rightBound = totalSize.x / 2;
            bool result = point.x > rightBound;
            distance = result ? point.x - rightBound : 0;
            return result;
        }

        /// <summary>
        /// 检查点是否在左边界之外
        /// </summary>
        public override bool IsPointOutsideLeft(Vector2 point, out float distance)
        {
            float leftBound = -totalSize.x / 2;
            bool result = point.x < leftBound;
            distance = result ? leftBound - point.x : 0;
            return result;
        }

        /// <summary>
        /// 检查点是否在上边界之外
        /// </summary>
        public override bool IsPointOutsideTop(Vector2 point, out float distance)
        {
            float topBound = totalSize.y / 2;
            bool result = point.y > topBound;
            distance = result ? point.y - topBound : 0;
            return result;
        }

        /// <summary>
        /// 检查点是否在下边界之外
        /// </summary>
        public override bool IsPointOutsideBottom(Vector2 point, out float distance)
        {
            float bottomBound = -totalSize.y / 2;
            bool result = point.y < bottomBound;
            distance = result ? bottomBound - point.y : 0;
            return result;
        }

        /// <summary>
        /// 从Boss围栏中移除道具
        /// </summary>
        public override void RemovePropFromBossFence(BossFenceBehavior fence)
        {
            // 让所有场地块从指定的围栏中移除道具
            foreach (var chunk in chunks)
            {
                chunk.RemovePropFromBossFence(fence);
            }
        }

        /// <summary>
        /// 清理场地
        /// </summary>
        public override void Clear()
        {
            // 销毁所有场地块
            foreach (var chunk in chunks)
            {
                Object.Destroy(chunk.gameObject);
            }
            chunks.Clear();

            // 销毁所有边界
            foreach (var border in borders)
            {
                Object.Destroy(border.gameObject);
            }
            borders.Clear();
        }
        
        /// <summary>
        /// 获取场景边界
        /// </summary>
        /// <returns>矩形场地的边界</returns>
        public override Bounds GetSceneBounds()
        {
            // 返回矩形场地的边界，中心在原点
            return new Bounds(Vector3.zero, new Vector3(totalSize.x, totalSize.y, 0));
        }
    }
}