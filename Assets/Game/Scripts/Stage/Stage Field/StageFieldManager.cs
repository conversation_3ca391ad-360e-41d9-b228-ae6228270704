using UncleChenGames.Bossfight;
using Uncle<PERSON>henGames.Extensions;
using Uncle<PERSON>henGames.Timeline.Bossfight;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;

namespace UncleChenGames
{
    /// <summary>
    /// 关卡场地管理器
    /// 负责管理场地类型、背景、边界围栏和场地行为
    /// </summary>
    public class StageFieldManager : MonoBehaviour
    {
        private static StageFieldManager instance; // 单例实例

        [SerializeField] BossfightDatabase bossfightDatabase; // Boss战数据库

        /// <summary>
        /// 获取当前关卡类型
        /// </summary>
        public StageType StageType { get; private set; }
        /// <summary>
        /// 获取背景预制体
        /// </summary>
        public GameObject BackgroundPrefab { get; private set; }

        /// <summary>
        /// 获取当前Boss围栏行为
        /// </summary>
        public BossFenceBehavior Fence { get; private set; }

        private IFieldBehavior field; // 场地行为接口
        private Dictionary<BossType, BossFenceBehavior> fences; // Boss类型与围栏的映射字典

        /// <summary>
        /// 唤醒时初始化单例
        /// </summary>
        private void Awake()
        {
            // 设置单例实例
            instance = this;
        }

        /// <summary>
        /// 初始化关卡场地管理器
        /// </summary>
        /// <param name="stageData">关卡数据</param>
        /// <param name="director">播放指导器</param>
        public void Init(StageData stageData, PlayableDirector director)
        {
            // 保存当前关卡类型
            StageType = stageData.StageType;
            
            // 根据关卡类型创建对应的场地行为
            switch (stageData.StageType)
            {
                case StageType.Endless: field = new EndlessFieldBehavior(); break;
                case StageType.VerticalEndless: field = new VerticalFieldBehavior(); break;
                case StageType.HorizontalEndless: field = new HorizontalFieldBehavior(); break;
                case StageType.Rect: field = new RectFieldBehavior(); break;
            }

            // 初始化场地行为
            field.Init(stageData.StageFieldData, stageData.SpawnProp);
            
            // 如果启用了相机边界限制，设置相机边界
            if (stageData.UseCameraBounds && stageData.StageType == StageType.Rect)
            {
                // 获取场景边界并设置给相机管理器
                var sceneBounds = field.GetSceneBounds();
                StageController.CameraController.SetSceneBounds(sceneBounds);
            }

            // 初始化围栏字典
            fences = new Dictionary<BossType, BossFenceBehavior>();

            // 如果是Rect类型，不需要创建围栏
            if (StageType == StageType.Rect)
            {
                return; // 跳过围栏创建，节省资源
            }

            // 获取所有Boss资源
            var bossAssets = director.GetAssets<BossTrack, Boss>();

            // 为每个Boss类型创建对应的围栏
            for (int i = 0; i < bossAssets.Count; i++)
            {
                var bossAsset = bossAssets[i];
                var bossData = bossfightDatabase.GetBossfight(bossAsset.BossType);

                // 如果该Boss类型的围栏还未创建
                if (!fences.ContainsKey(bossData.BossType))
                {
                    // 实例化围栏预制体并获取围栏行为组件
                    var fence = Instantiate(bossData.FencePrefab).GetComponent<BossFenceBehavior>();
                    // 初始时隐藏围栏
                    fence.gameObject.SetActive(false);
                    // 初始化围栏
                    fence.Init();

                    // 将围栏添加到字典中
                    fences.Add(bossData.BossType, fence);
                }
            }
        }

        /// <summary>
        /// 生成指定类型的Boss围栏
        /// </summary>
        /// <param name="bossType">Boss类型</param>
        /// <param name="offset">偏移量</param>
        /// <returns>围栏中心位置</returns>
        public Vector2 SpawnFence(BossType bossType, Vector2 offset)
        {
            // Rect类型关卡使用固定边界，不需要额外的Boss围栏
            // 直接使用场地的边界作为战斗区域限制
            if (StageType == StageType.Rect)
            {
                Fence = null;
                // 使用场地的GetBossSpawnPosition方法获取正确的中心点
                return field.GetBossSpawnPosition(null, offset);
            }
            
            // 非Rect类型，正常生成围栏
            // 设置当前围栏（使用TryGetValue避免KeyNotFoundException）
            if (!fences.TryGetValue(bossType, out var fence))
            {
                Debug.LogError($"Fence not found for boss type: {bossType}. This might happen if trying to spawn a boss in a Rect stage type or if the boss fence was not properly initialized.");
                return Vector2.zero;
            }
            Fence = fence;
            
            // 激活围栏GameObject
            Fence.gameObject.SetActive(true);

            // 获取Boss生成位置
            var center = field.GetBossSpawnPosition(Fence, offset);

            // 在指定位置生成围栏
            Fence.SpawnFence(center);

            return center;
        }

        /// <summary>
        /// 移除当前围栏
        /// </summary>
        public void RemoveFence()
        {
            // 如果没有围栏，直接返回
            if (Fence == null) return;
            
            // 移除围栏
            Fence.RemoveFence();
            // 隐藏围栏GameObject
            Fence.gameObject.SetActive(false);
            // 清空当前围栏引用
            Fence = null;
        }

        /// <summary>
        /// 从围栏中移除道具
        /// </summary>
        public void RemovePropFromFence()
        {
            // 如果没有围栏，不执行移除道具操作
            if (Fence == null) return;
            
            // 从Boss围栏中移除道具
            field.RemovePropFromBossFence(Fence);
        }

        /// <summary>
        /// 每帧更新场地状态
        /// </summary>
        private void Update()
        {
            // 更新场地行为
            field.Update();
        }

        /// <summary>
        /// 验证位置是否有效
        /// </summary>
        /// <param name="position">位置</param>
        /// <param name="offset">偏移量</param>
        /// <param name="withFence">是否考虑围栏</param>
        /// <returns>位置是否有效</returns>
        public bool ValidatePosition(Vector2 position, Vector2 offset, bool withFence = true)
        {
            // 先进行场地边界验证（考虑offset）
            var fieldValid = true;
            
            // 如果offset不为零，检查四个方向的边界
            if (offset.magnitude > 0)
            {
                // 检查右边界
                if (offset.x > 0 && !instance.field.ValidatePosition(position + Vector2.right * offset.x))
                    fieldValid = false;
                // 检查左边界
                if (offset.x > 0 && !instance.field.ValidatePosition(position + Vector2.left * offset.x))
                    fieldValid = false;
                // 检查上边界
                if (offset.y > 0 && !instance.field.ValidatePosition(position + Vector2.up * offset.y))
                    fieldValid = false;
                // 检查下边界
                if (offset.y > 0 && !instance.field.ValidatePosition(position + Vector2.down * offset.y))
                    fieldValid = false;
            }
            else
            {
                // 如果没有偏移，使用原始位置验证
                fieldValid = instance.field.ValidatePosition(position);
            }
            
            // 围栏验证保持不变
            var isFenceValid = true;
            // 如果存在围栏且需要验证围栏
            if (Fence != null && withFence)
            {
                // 验证围栏位置
                isFenceValid = Fence.ValidatePosition(position, offset);
            }
            
            // 返回场地验证和围栏验证的结果
            return fieldValid && isFenceValid;
        }

        /// <summary>
        /// 获取边界上的随机位置
        /// </summary>
        /// <returns>边界上的随机位置</returns>
        public Vector2 GetRandomPositionOnBorder()
        {
            // 返回场地边界上的随机位置
            return instance.field.GetRandomPositionOnBorder();
        }

        /// <summary>
        /// 获取边界内的随机位置
        /// </summary>
        /// <returns>边界内的随机位置</returns>
        public Vector2 GetRandomPositionInside()
        {
            // 返回场地边界内的随机位置
            return instance.field.GetRandomPositionInside();
        }

        /// <summary>
        /// 获取敌人生成位置
        /// </summary>
        /// <param name="preferOutsideCamera">是否优先在摄像机外生成</param>
        /// <returns>敌人生成位置</returns>
        public Vector2 GetEnemySpawnPosition(bool preferOutsideCamera = true)
        {
            // 返回场地的敌人生成位置
            return instance.field.GetEnemySpawnPosition(preferOutsideCamera);
        }

        /// <summary>
        /// 检查点是否在场地右侧之外
        /// </summary>
        /// <param name="point">检查点</param>
        /// <param name="distance">输出距离</param>
        /// <returns>是否在右侧之外</returns>
        public bool IsPointOutsideFieldRight(Vector2 point, out float distance)
        {
            return field.IsPointOutsideRight(point, out distance);
        }

        /// <summary>
        /// 检查点是否在场地左侧之外
        /// </summary>
        /// <param name="point">检查点</param>
        /// <param name="distance">输出距离</param>
        /// <returns>是否在左侧之外</returns>
        public bool IsPointOutsideFieldLeft(Vector2 point, out float distance)
        {
            return field.IsPointOutsideLeft(point, out distance);
        }

        /// <summary>
        /// 检查点是否在场地顶部之外
        /// </summary>
        /// <param name="point">检查点</param>
        /// <param name="distance">输出距离</param>
        /// <returns>是否在顶部之外</returns>
        public bool IsPointOutsideFieldTop(Vector2 point, out float distance)
        {
            return field.IsPointOutsideTop(point, out distance);
        }

        /// <summary>
        /// 检查点是否在场地底部之外
        /// </summary>
        /// <param name="point">检查点</param>
        /// <param name="distance">输出距离</param>
        /// <returns>是否在底部之外</returns>
        public bool IsPointOutsideFieldBottom(Vector2 point, out float distance)
        {
            return field.IsPointOutsideBottom(point, out distance);
        }

        /// <summary>
        /// 获取围栏内或场地内的随机点
        /// 用于Boss在战斗时获取移动目标位置，兼容有围栏和无围栏的情况
        /// </summary>
        /// <param name="offset">边界偏移量</param>
        /// <returns>围栏内或场地内的随机位置</returns>
        public Vector2 GetRandomPointInsideFenceOrField(float offset)
        {
            // 如果有围栏，使用围栏的随机点
            if (Fence != null)
                return Fence.GetRandomPointInside(offset);
            
            // 否则使用场地的随机点
            return instance.field.GetRandomPositionInside();
        }
    }
}